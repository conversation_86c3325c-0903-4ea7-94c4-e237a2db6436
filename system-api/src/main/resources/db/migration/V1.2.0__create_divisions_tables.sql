CREATE TABLE IF NOT EXISTS `skylink`.`china_administrative_divisions`
(
    `code`        VARCHAR(12) NOT NULL COMMENT '行政区划代码 (GB/T 2260)',
    `name`        VARCHAR(50) NOT NULL COMMENT '名称',
    `parent_code` VARCHAR(12) COMMENT '父级代码',
    `level`       TINYINT COMMENT '层级 (1:省/直辖市, 2:市, 3:区/县)',
    PRIMARY KEY (`code`),
    INDEX `idx_name` (`name`),
    INDEX `idx_parent_code` (`parent_code`),
    INDEX `idx_level` (`level`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COMMENT ='中国行政区划表';