package com.mediacomm.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * 国标设备分页响应VO.
 */
@Data
@Schema(name = "GbDevicePageVo", description = "国标设备分页响应")
public class GbDevicePageVo {
  @Schema(description = "设备列表")
  private List<GbDeviceVo> list;
  
  @Schema(description = "分页信息")
  private PaginationVo pagination;
  
  /**
   * 分页信息内部类.
   */
  @Data
  @Schema(name = "PaginationVo", description = "分页信息")
  public static class PaginationVo {
    @Schema(description = "总记录数")
    private long total;
    
    @Schema(description = "当前页码")
    private int pageNum;
    
    @Schema(description = "每页数量")
    private int pageSize;
  }
}
