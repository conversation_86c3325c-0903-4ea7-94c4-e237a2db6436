package com.mediacomm.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Strings;
import com.mediacomm.entity.dao.DeviceModel;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.vo.GbDevicePageVo;
import com.mediacomm.entity.vo.GbDeviceVo;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.entity.vo.PageResult;
import com.mediacomm.system.base.service.impl.SkyLinkServiceImpl;
import com.mediacomm.system.mapper.AssetMapper;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * .
 */
@Slf4j
@Service
public class KvmAssetService extends SkyLinkServiceImpl<AssetMapper, KvmAsset> {

  @Autowired
  KvmMasterService ks;
  @Autowired
  DeviceModelService dm;

  /**
   * 分页联表查询所有外设及关联主机和设备型号.
   *
   * @param currentPage 当前页码.
   * @param pageSize    每页大小.
   * @param masterId    主机Id.
   * @param name        外设名称.
   * @param hardcode    外设hardcode.
   * @param id          外设主机内Id.
   * @return Collection.
   */
  public PageResult<KvmAssetVo> allByPage(Integer currentPage, Integer pageSize, String masterId,
                              String name, String hardcode, Integer id) {
    LambdaQueryWrapper<KvmAsset> wrapper = Wrappers.lambdaQuery(KvmAsset.class);
    if (!Strings.isNullOrEmpty(masterId)) {
      wrapper.and(wrap -> wrap.eq(KvmAsset::getMasterId, masterId));
    }
    if (!Strings.isNullOrEmpty(name)) {
      wrapper.and(wrap -> wrap.like(KvmAsset::getName, "%" + name + "%"));
    }
    if (!Strings.isNullOrEmpty(hardcode)) {
      wrapper.and(wrap -> wrap.like(KvmAsset::getHardcode, "%" + hardcode + "%"));
    }
    if (id != null) {
      wrapper.and(wrap -> wrap.like(KvmAsset::getDeviceId, "%" + id + "%"));
    }
    return allByPage(wrapper, currentPage, pageSize);
  }

    /**
   * 分页查询指定设备类型的外设信息.
   * 
   * @param currentPage 当前页码，用于指定查询结果的起始页.
   * @param pageSize 每页显示的记录数量，用于控制每页展示的外设信息数量.
   * @param deviceType 设备类型，若不为 null，则根据该设备类型筛选外设信息；若为 null，则查询所有外设信息.
   * @return 包含分页信息和外设信息列表的 PageResult 对象.
   */
  public PageResult<KvmAssetVo> allByPage(Integer currentPage, Integer pageSize, DeviceType... deviceType) {
    LambdaQueryWrapper<KvmAsset> wrapper = Wrappers.lambdaQuery(KvmAsset.class);
    if (deviceType != null && deviceType.length > 0) {
      Integer[] deviceTypeIds = Arrays.stream(deviceType)
        .map(DeviceType::getDeviceTypeId)
        .toArray(Integer[]::new);
      Object[] objectDeviceTypeIds = Arrays.stream(deviceTypeIds).toArray();
      wrapper.in(KvmAsset::getDeviceModel, objectDeviceTypeIds);
    }
    return allByPage(wrapper, currentPage, pageSize);
  }

  public PageResult<KvmAssetVo> allByPage(LambdaQueryWrapper<KvmAsset> wrapper, 
                                          Integer currentPage, Integer pageSize) {
    Page<KvmAsset> page = new Page<>(currentPage, pageSize);
    List<KvmAssetVo> vos = new ArrayList<>();
    page(page, wrapper).getRecords().forEach(kvmAsset -> {
      KvmAssetVo vo = Optional.ofNullable(kvmAsset).map(KvmAssetVo::new).orElse(null);
      Optional.ofNullable(vo).ifPresent(this::addAssetInfo);
      vos.add(vo);
    });
    return new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal(),
        page.getPages(), vos);
  }

  /**
   * 根据主机ID查找所属的所有外设.
   *
   * @param masterId 主机ID.
   * @return .
   */
  public Collection<KvmAssetVo> allByMasterId(String masterId) {
    return baseMapper.allByMasterId(masterId);
  }

  /**
   * 根据设备类型ID查找外设信息.
   *
   * @param modelId .
   * @return .
   */
  public Collection<KvmAssetVo> allByDeviceModelId(Integer modelId, String masterId) {
    Collection<KvmAssetVo> vos = new ArrayList<>();
    LambdaQueryWrapper<KvmAsset> wrapper = Wrappers.lambdaQuery(KvmAsset.class)
        .eq(KvmAsset::getDeviceModel, modelId)
        .and(wrap -> wrap.eq(KvmAsset::getMasterId, masterId));
    list(wrapper).forEach(kvmAsset -> {
      KvmAssetVo vo = Optional.ofNullable(kvmAsset).map(KvmAssetVo::new).orElse(null);
      Optional.ofNullable(vo).ifPresent(this::addAssetInfo);
      vos.add(vo);
    });
    return vos;
  }

  /**
   * 根据型号类别查找外设.
   *
   * @param masterId 主机ID.
   * @param type 型号类别.
   * @return .
   */
  public Collection<KvmAssetVo> allByMasterIdAndSubSystem(String masterId, SubSystemType type) {
    Collection<KvmAssetVo> vos = allByMasterId(masterId);
    vos.removeIf(kvmAssetVo -> {
      DeviceType currentType = DeviceType.valueOf(kvmAssetVo.getDeviceType());
      return currentType.getSubSystem() != type;
    });
    return vos;
  }

  /**
   * 根据分组Id查询所有Tx或Rx.
   *
   * @param groupId 分组Id.
   * @return Collection.
   */
  public Collection<KvmAssetVo> allByGroupId(Integer groupId) {
    Collection<KvmAssetVo> vos = new ArrayList<>();
    baseMapper.findByGroupId(groupId).forEach(kvmAsset -> {
      KvmAssetVo vo = Optional.ofNullable(kvmAsset).map(KvmAssetVo::new).orElse(null);
      Optional.ofNullable(vo).ifPresent(this::addAssetInfo);
      vos.add(vo);
    });
    return vos;
  }

  /**
   * 通过硬件编码查找外设.
   *
   * @param hardcode 硬件编码.
   * @return KvmAsset.
   */
  public KvmAssetVo oneByHardcode(@NonNull String hardcode) {
    LambdaQueryWrapper<KvmAsset> wrapper = Wrappers.lambdaQuery(KvmAsset.class)
        .eq(KvmAsset::getHardcode, hardcode);
    KvmAssetVo vo = Optional.ofNullable(getOne(wrapper)).map(KvmAssetVo::new).orElse(null);
    Optional.ofNullable(vo).ifPresent(this::addAssetInfo);
    return vo;
  }

  /**
   * 通过设备ID查找外设.
   *
   * @param assetId 设备ID.
   * @return KvmAsset.
   */
  public KvmAssetVo oneById(String assetId) {
    KvmAssetVo vo = Optional.ofNullable(getById(assetId)).map(KvmAssetVo::new).orElse(null);
    Optional.ofNullable(vo).ifPresent(this::addAssetInfo);
    return vo;
  }

  /**
   * 通过设备ID查找外设.
   *
   * @param deviceId 设备ID.
   * @return KvmAsset.
   */
  public KvmAssetVo oneByDeviceId(Integer deviceId, String masterId) {
    LambdaQueryWrapper<KvmAsset> wrapper =
        Wrappers.lambdaQuery(KvmAsset.class).eq(KvmAsset::getDeviceId, deviceId)
            .and(item -> item.eq(KvmAsset::getMasterId, masterId));
    KvmAssetVo vo = Optional.ofNullable(getOne(wrapper)).map(KvmAssetVo::new).orElse(null);
    Optional.ofNullable(vo).ifPresent(this::addAssetInfo);
    return vo;
  }

  public KvmAssetVo oneByDeviceIdAndModelId(Integer deviceId, Integer modelId, String masterId) {
    LambdaQueryWrapper<KvmAsset> wrapper =
            Wrappers.lambdaQuery(KvmAsset.class).eq(KvmAsset::getDeviceId, deviceId)
                    .and(item -> item.eq(KvmAsset::getMasterId, masterId))
                    .and(item -> item.eq(KvmAsset::getDeviceModel, modelId));
    KvmAssetVo vo = Optional.ofNullable(getOne(wrapper)).map(KvmAssetVo::new).orElse(null);
    Optional.ofNullable(vo).ifPresent(this::addAssetInfo);
    return vo;
  }

  /**
   * KvmAsset更新或保存.
   */
  public void saveOrUpdateKvmAsset(KvmAsset asset, @NotNull String masterId) {
    asset.setMasterId(masterId);
    KvmAsset old = oneByHardcode(asset.getHardcode());
    if (old != null) {
      asset.setAlias(old.getAlias()); // 设备别名不变
      asset.setAssetId(old.getAssetId());
      asset.setDeviceModel(old.getDeviceModel());
      asset.setCollectorProperties(old.getCollectorProperties());
      updateById(asset);
    } else {
      save(asset);
    }
  }

  /**
   * 根据硬件编码删除外设.
   */
  public void delByHardcode(String hardcode) {
    remove(Wrappers.lambdaQuery(KvmAsset.class).eq(KvmAsset::getHardcode, hardcode));
  }

  /**
   * 从指定的设备类型的外设中删除已经不存在于接口数据中的外设.
   * @param newHardcodeList 同一类型的外设列表的序列号列表.
   * @param deviceType 指定的设备类型.
   * @param masterId 指定的主机.
   */
  public void delBatchNotExistByHardcode(Collection<String> newHardcodeList, String masterId,
                                         DeviceType... deviceType) {
    // 通过此sql可以查询出重复的device_id
    // select `device_id`, COUNT(`device_id`) as 计数 from (select * FROM kvm_asset
    // where master_id='d68f19adbba84c998eaf704f7728b057') as fuzhu GROUP by fuzhu.device_id
    // HAVING COUNT(fuzhu.`device_id`) > 1;
    if (newHardcodeList != null && !newHardcodeList.isEmpty()) {
      Collection<String> oldList = new ArrayList<>();
      for (DeviceType type : deviceType) {
        oldList.addAll(allByDeviceModelId(type.getDeviceTypeId(), masterId).stream()
                .map(KvmAssetVo::getHardcode).toList());
      }
      for (String hardcode : oldList) {
        if (!newHardcodeList.contains(hardcode)) {
          delByHardcode(hardcode);
          log.info("{} device has been delete.", hardcode);
        }
      }
    }
  }

  private void addAssetInfo(KvmAssetVo vo) {
    KvmMaster master = ks.getById(vo.getMasterId());
    DeviceModel model = dm.getById(vo.getDeviceModel());
    Optional.ofNullable(master).ifPresent(m -> vo.setMasterName(master.getName()));
    Optional.ofNullable(model).ifPresent(m -> {
      vo.setModelName(model.getModelName());
      vo.setDeviceType(model.getDeviceType());
      vo.setSubSystemType(model.getSubSystem());
    });
  }

  /**
   * 根据行政区划代码分页查询国标设备列表.
   *
   * @param regionCode 行政区划代码，若为null则查询所有设备
   * @param pageNum    当前页码，若为null或0则返回所有数据
   * @param pageSize   每页数量，若为null或0则返回所有数据
   * @return 国标设备分页响应
   */
  public GbDevicePageVo getGbDevicesByRegion(String regionCode, Integer pageNum, Integer pageSize) {
    LambdaQueryWrapper<KvmAsset> wrapper = Wrappers.lambdaQuery(KvmAsset.class);

    // 过滤条件：设备类型为国标设备
    wrapper.eq(KvmAsset::getDeviceModel, DeviceType.HIKVISION_SECURE_IPC.getDeviceTypeId());

    // 过滤条件：hardcode不为空，并且是20位
    wrapper.isNotNull(KvmAsset::getHardcode)
        .ne(KvmAsset::getHardcode, "")
        .like(KvmAsset::getHardcode, "%20");

    // 行政区划代码补全
    int length = 6 - regionCode.length();
    if (regionCode.length() < 6) {
      regionCode = regionCode + "000000".substring(0, length);
    }

    // 如果提供了行政区划代码，则按前缀匹配
    if (!Strings.isNullOrEmpty(regionCode)) {
      wrapper.likeRight(KvmAsset::getHardcode, regionCode);
    }

    // 排序,保证分页稳定性
    wrapper.orderByDesc(KvmAsset::getHardcode);

    GbDevicePageVo result = new GbDevicePageVo();
    List<GbDeviceVo> deviceList = new ArrayList<>();

    // 判断是否需要分页
    boolean needPagination = pageNum != null && pageSize != null && pageNum > 0 && pageSize > 0;

    if (needPagination) {
      // 分页查询
      Page<KvmAsset> page = new Page<>(pageNum, pageSize);
      Page<KvmAsset> pageResult = page(page, wrapper);

      // 转换为响应VO
      pageResult.getRecords().forEach(asset -> {
        GbDeviceVo deviceVo = new GbDeviceVo();
        deviceVo.setAssetId(asset.getAssetId());
        deviceVo.setName(asset.getName());
        deviceList.add(deviceVo);
      });

      // 设置分页信息
      GbDevicePageVo.PaginationVo pagination = new GbDevicePageVo.PaginationVo();
      pagination.setTotal(pageResult.getTotal());
      pagination.setPageNum((int) pageResult.getCurrent());
      pagination.setPageSize((int) pageResult.getSize());
      result.setPagination(pagination);
    } else {
      // 查询所有数据
      List<KvmAsset> allAssets = list(wrapper);

      // 转换为响应VO
      allAssets.forEach(asset -> {
        GbDeviceVo deviceVo = new GbDeviceVo();
        deviceVo.setAssetId(asset.getAssetId());
        deviceVo.setName(asset.getName());
        deviceList.add(deviceVo);
      });

      // 设置分页信息（显示总数）
      GbDevicePageVo.PaginationVo pagination = new GbDevicePageVo.PaginationVo();
      pagination.setTotal(allAssets.size());
      pagination.setPageNum(1);
      pagination.setPageSize(allAssets.size());
      result.setPagination(pagination);
    }

    result.setList(deviceList);
    return result;
  }

}
